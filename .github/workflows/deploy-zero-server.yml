name: Deploy Zero Server

on:
  push:
    branches:
      - main          # Deploy to production
      - staging       # Deploy to staging
      - preview       # Deploy to dev/preview
    paths:
      - 'apps/zero-server/**'
      - 'packages/zero-schema/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
        - dev
        - staging
        - prod

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 9.12.0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'pnpm'
        cache-dependency-path: 'pnpm-lock.yaml'

    - name: Cache pnpm store
      uses: actions/cache@v4
      with:
        path: ~/.pnpm-store
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Cache system dependencies
      uses: actions/cache@v4
      with:
        path: /var/cache/apt
        key: ${{ runner.os }}-apt-cache-${{ hashFiles('.github/workflows/deploy-zero-server.yml') }}
        restore-keys: |
          ${{ runner.os }}-apt-cache-

    - name: Cache Fly CLI
      id: cache-fly
      uses: actions/cache@v4
      with:
        path: ~/.fly
        key: ${{ runner.os }}-fly-cli-${{ hashFiles('.github/workflows/deploy-zero-server.yml') }}
        restore-keys: |
          ${{ runner.os }}-fly-cli-

    - name: Cache zero-schema build
      uses: actions/cache@v4
      with:
        path: |
          packages/zero-schema/dist
          packages/zero-schema/tsconfig.tsbuildinfo
        key: ${{ runner.os }}-zero-schema-${{ hashFiles('packages/zero-schema/src/**/*', 'packages/zero-schema/tsconfig.json') }}
        restore-keys: |
          ${{ runner.os }}-zero-schema-

    - name: Install system dependencies
      run: sudo apt-get update && sudo apt-get install -y libreadline-dev

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Setup Fly CLI
      if: steps.cache-fly.outputs.cache-hit != 'true'
      run: |
        curl -L https://fly.io/install.sh | sh
        echo "$HOME/.fly/bin" >> $GITHUB_PATH
    
    - name: Add Fly CLI to PATH (cached)
      if: steps.cache-fly.outputs.cache-hit == 'true'
      run: echo "$HOME/.fly/bin" >> $GITHUB_PATH

    - name: Determine environment
      id: env
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          echo "environment=$(echo '${{ github.event.inputs.environment }}' | tr '[:lower:]' '[:upper:]')" >> $GITHUB_OUTPUT
        elif [ "${{ github.ref }}" = "refs/heads/main" ]; then
          echo "environment=PROD" >> $GITHUB_OUTPUT
        elif [ "${{ github.ref }}" = "refs/heads/staging" ]; then
          echo "environment=STAGING" >> $GITHUB_OUTPUT
        elif [ "${{ github.ref }}" = "refs/heads/preview" ]; then
          echo "environment=DEV" >> $GITHUB_OUTPUT
        else
          echo "❌ Unsupported branch: ${{ github.ref_name }}"
          echo "Only main, staging, and preview branches are supported for automatic deployment"
          exit 1
        fi

    - name: Deploy to Dev
      if: steps.env.outputs.environment == 'DEV'
      run: |
        cd apps/zero-server
        # For testing, use hardcoded values in fly-dev.toml (skip setting secrets)
        echo "🔧 Using hardcoded values in fly-dev.toml for testing"
        fly deploy --config deploy/fly-dev.toml --app axc-dev-zero-cache
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy to Staging
      if: steps.env.outputs.environment == 'STAGING'
      run: |
        cd apps/zero-server
        # For testing, use hardcoded values in fly-staging.toml (skip setting secrets)
        echo "🔧 Using hardcoded values in fly-staging.toml for testing"
        fly deploy --config deploy/fly-staging.toml --app axc-staging-zero-cache
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy to Production
      if: steps.env.outputs.environment == 'PROD'
      run: |
        cd apps/zero-server
        # For testing, use hardcoded values in fly.toml (skip setting secrets)
        echo "🔧 Using hardcoded values in fly.toml for production"
        fly deploy --config fly.toml --app axc-prod-zero-cache
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy Permissions
      run: |
        cd apps/zero-server
        ZERO_UPSTREAM_DB="${{ secrets[format('SUPABASE_CONNECTION_{0}', steps.env.outputs.environment)] }}" npx zero-deploy-permissions --schema-path="../../packages/zero-schema/src/schema.ts"