app = "axc-dev-zero-cache"
primary_region = "ams"

[build]
image = "registry.hub.docker.com/rocicorp/zero:0.21.2025062401"

[http_service]
internal_port = 4848
force_https = true
auto_stop_machines = 'off'
min_machines_running = 1

[[http_service.checks]]
grace_period = "10s"
interval = "30s"
method = "GET"
timeout = "5s"
path = "/"

[[vm]]
memory = "2gb"
cpu_kind = "shared"
cpus = 2

[mounts]
source = "sqlite_db_dev"
destination = "/data"

[env]
ZERO_REPLICA_FILE = "/data/sync-replica.db"
ZERO_UPSTREAM_DB = "postgres://postgres:<EMAIL>:6543/postgres"
ZERO_CVR_DB = "postgres://postgres:<EMAIL>:6543/postgres"
ZERO_CHANGE_DB = "postgres://postgres:<EMAIL>:6543/postgres"
ZERO_PUSH_URL = "https://preview-dot-psychic-valve-439013-d2.lm.r.appspot.com/push"
ZERO_AUTH_SECRET = "6gnPcWawXeaB2AN0hbC6MSJA4r4M/ZezZ6p/nKtEDEkknQjKr4S9SglLhvJGi4i/CltHnhKksoWep/6us+bXuw=="
LOG_LEVEL = "debug"