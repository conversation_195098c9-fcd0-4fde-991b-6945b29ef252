import dotenv from "dotenv";
import { observeOpenAI } from "langfuse";
import OpenAI from "openai";

dotenv.config();

export const LLM_MODEL_ID = "google/gemini-2.5-pro";

export const callLLM = async (
  prompt: any,
  langfuseConfig: any,
  model: string,
  additionalParams: any = {
    parse: true,
  },
  additionalMessages: any = []
) => {
  try {
    const url = "https://openrouter.ai/api/v1";

    const client = new OpenAI({
      baseURL: url,
      apiKey: process.env.OPENROUTER_API_KEY,
    });
    console.log("langfuseConfig:", langfuseConfig);
    console.log("langfuseConfig.temperature:", langfuseConfig.temperature);
    console.log("langfuseConfig.schema:", langfuseConfig.schema);
    console.log("langfuseConfig.response_format:", langfuseConfig.response_format);
    console.log("typeof langfuseConfig.schema:", typeof langfuseConfig.schema);

    const langfuseClient = observeOpenAI(client);

    // Use llm_model_id from langfuse config if available, otherwise fallback to the passed model or LLM_MODEL_ID
    const selectedModel = langfuseConfig?.llm_model_id || model || LLM_MODEL_ID;
    console.log("Selected model:", selectedModel, "from langfuse config:", !!langfuseConfig?.llm_model_id);

    // Build messages array conditionally
    let messages = [];
    
    // Only add prompt as user message if it's not empty
    if (prompt && prompt.trim && prompt.trim().length > 0) {
      messages.push({ role: "user", content: prompt });
    }
    
    // Add additional messages
    messages = [...messages, ...additionalMessages];

    // Prepare the parameters object
    const apiParams: any = {
      model: selectedModel,
      temperature: langfuseConfig.temperature,
      messages: messages, // Use the conditionally built messages array
      ...additionalParams,
    };

    // Check for response_format first (this is the correct property for JSON schema)
    if (langfuseConfig.response_format && typeof langfuseConfig.response_format === "object") {
      apiParams.response_format = langfuseConfig.response_format;
      console.log("Using response_format from langfuseConfig:", langfuseConfig.response_format);
    }
    // Fallback to schema if response_format doesn't exist
    else if (langfuseConfig.schema && typeof langfuseConfig.schema === "object") {
      apiParams.response_format = langfuseConfig.schema;
      console.log("Using schema as response_format:", langfuseConfig.schema);
    }

    console.log("Final API params:", JSON.stringify(apiParams, null, 2));

    const response = await langfuseClient.chat.completions.create(apiParams);

    // Debug the full response
    console.log("🔍 Full OpenRouter response:", JSON.stringify(response, null, 2));
    console.log("🔍 Response choices:", response.choices);
    console.log("🔍 First choice:", response.choices[0]);
    console.log("🔍 Message content:", response.choices[0]?.message?.content);
    console.log("🔍 Finish reason:", response.choices[0]?.finish_reason);

    const messageContent = response.choices[0].message.content;
    console.log("🔍 Raw message content:", messageContent);
    console.log("🔍 Message content type:", typeof messageContent);
    console.log("🔍 Message content length:", messageContent?.length);

    const cleanedJson = clean(messageContent || "");
    console.log("🔍 After cleaning:", cleanedJson);
    console.log("cleanedJson:", {additionalParams});
    if (additionalParams.parse) {
      try {
        return JSON.parse(cleanedJson);
      } catch (parseError) {
        console.error("JSON parse error:", parseError);
        console.error("Content that failed to parse:", cleanedJson);
        throw new Error(`Failed to parse JSON response: ${parseError.message}`);
      }
    }
    return cleanedJson;
  } catch (error) {
    console.error({ error });
    throw new Error(`Failed to call LLM: ${error}`);
  }
};

const clean = (text: string) => text.trim().replace(/^```(?:\w*\n)?|```$/g, "");
