import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM, LLM_MODEL_ID } from '../utils/callLLM.js';
import {
  ContentGenerationParams,
  GeneratedContent
} from '../types/content-generation.js';
import {
  createPromptVariables,
  normalizeCompiledPrompt,
  validateCompiledPrompt,
  validateContentGenerationParams,
  ContentGenerationError
} from '../utils/content-generation.js';
import { ServerBlockNoteEditor } from '@blocknote/server-util';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

// Legacy interface for backward compatibility
interface TaskContentParams extends ContentGenerationParams {}

/**
 * Converts text content to BlockNote blocks format using official BlockNote server utilities
 */
async function convertTextToBlocks(text: string): Promise<any[]> {
  if (!text || text.trim() === '') {
    return [];
  }

  try {
    // Create a server-side BlockNote editor instance
    const editor = ServerBlockNoteEditor.create();

    // Use BlockNote's official markdown parser to convert text to blocks
    const blocks = await editor.tryParseMarkdownToBlocks(text);

    return blocks;
  } catch (error) {
    console.error('Error converting text to blocks with BlockNote:', error);

    // Fallback to simple paragraph blocks if BlockNote parsing fails
    const paragraphs = text.split('\n\n').filter(p => p.trim() !== '');

    return paragraphs.map(paragraph => ({
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: paragraph.trim(),
          styles: {},
        },
      ],
    }));
  }
}

/**
 * Compiles a Langfuse prompt with the given variables
 */
async function compilePrompt(params: ContentGenerationParams) {
  const prompt = await langfuse.getPrompt("generate_content_body_v2", undefined, { label: "production" });
  const promptVariables = createPromptVariables(params);
  const compiled = prompt.compile(promptVariables);
  
  // Return both compiled messages and config
  return {
    messages: compiled,
    config: prompt.config || {}
  };
}

/**
 * Generates content for a single task using Langfuse prompt and LLM
 */
export async function generateTaskContent(params: TaskContentParams): Promise<GeneratedContent> {
  try {
    // Validate input parameters
    validateContentGenerationParams(params);

    const { messages: compiledMessages, config: langfuseConfig } = await compilePrompt(params);

    // Debug logging to see what Langfuse config contains
    console.log("🔧 Langfuse config:", JSON.stringify(langfuseConfig, null, 2));

    // Merge Langfuse config with any overrides
    const llmConfig = {
      temperature: 0.7,
      max_tokens: 4000,
      ...(typeof langfuseConfig === 'object' && langfuseConfig !== null ? langfuseConfig : {}),
    };

    console.log("🚀 Final LLM config:", JSON.stringify(llmConfig, null, 2));

    // Filter out empty messages that might confuse the LLM
    const filteredMessages = Array.isArray(compiledMessages) 
      ? compiledMessages.filter(msg => msg.content && msg.content.trim().length > 0)
      : compiledMessages;

    // Debug the compiled messages structure
    console.log("📝 Original messages count:", Array.isArray(compiledMessages) ? compiledMessages.length : 'not array');
    console.log("📝 Filtered messages count:", Array.isArray(filteredMessages) ? filteredMessages.length : 'not array');
    
    // Log each filtered message in detail
    if (Array.isArray(filteredMessages)) {
      filteredMessages.forEach((msg, index) => {
        console.log(`📋 Message ${index}:`, {
          role: msg.role,
          contentLength: msg.content?.length || 0,
          contentPreview: msg.content?.substring(0, 150) + '...'
        });
      });
    }

    // Validate we have proper messages
    if (!Array.isArray(filteredMessages) || filteredMessages.length === 0) {
      throw new ContentGenerationError('No valid messages found after filtering');
    }

    // Check if we have at least a system and user message
    const hasSystem = filteredMessages.some(msg => msg.role === 'system');
    const hasUser = filteredMessages.some(msg => msg.role === 'user');
    console.log("📊 Message validation:", { hasSystem, hasUser, totalMessages: filteredMessages.length });

    if (!hasSystem || !hasUser) {
      console.warn("⚠️ Missing required message types - system:", hasSystem, "user:", hasUser);
    }

    // Call LLM with filtered messages
    console.log("🤖 Calling LLM with", filteredMessages.length, "messages...");
    const response = await callLLM(
      "",
      llmConfig,
      LLM_MODEL_ID, // fallback model
      { parse: false },
      filteredMessages
    );

    console.log("✅ LLM Response received:");
    console.log("   📏 Response length:", response?.length || 0);
    console.log("   📝 Response type:", typeof response);
    console.log("   🔍 Response preview:", response?.substring(0, 200) + '...');
    console.log("   🔍 Full response (if short):", response?.length < 100 ? response : 'Too long to display');

    // Check if response is empty
    if (!response || response.trim().length === 0) {
      console.warn("⚠️ LLM returned empty response");
      console.warn("⚠️ This could be due to:");
      console.warn("   - Message format issues");
      console.warn("   - API rate limiting");
      console.warn("   - Model restrictions");
      console.warn("   - Prompt content triggering safety filters");
      
      // Try a simple test to see if the API is working
      console.log("🧪 Testing with a simple message...");
      try {
        const testResponse = await callLLM(
          "",
          { temperature: 0.7, max_tokens: 100 },
          LLM_MODEL_ID, // fallback model
          { parse: false },
          [{ role: "user", content: "Say hello world" }]
        );
        console.log("🧪 Test response:", testResponse);
      } catch (testError) {
        console.error("🧪 Test call failed:", testError);
      }
      
      throw new ContentGenerationError('LLM returned empty response. Please try again.');
    }

    // Convert text content to BlockNote blocks using official server utilities
    const contentBlocks = await convertTextToBlocks(response);

    // Return standardized response format
    return {
      content: response, // Keep for backward compatibility
      content_editor_template: contentBlocks,
      visual_description: null,
      seo_keywords: [],
      trend_keywords: []
    };

  } catch (error) {
    console.error("❌ Full error in generateTaskContent:", error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    if (error instanceof ContentGenerationError) {
      console.warn(`   ❌ Content generation validation failed: ${errorMessage}`);
      throw error;
    }

    if (errorMessage.includes('generate_content_body_v2')) {
      console.warn(`   ❌ Langfuse prompt failed: ${errorMessage}`);
      throw new ContentGenerationError(`Prompt compilation failed: ${errorMessage}`);
    }

    console.error('Error generating task content:', error);
    throw new ContentGenerationError(
      'Failed to generate task content. Please check your API key and try again.',
      undefined,
      undefined,
      error instanceof Error ? error : new Error(errorMessage)
    );
  }
}
