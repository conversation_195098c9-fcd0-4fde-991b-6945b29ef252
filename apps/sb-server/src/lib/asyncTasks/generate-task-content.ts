import { generateTaskContent } from '../services/generate-task-content.js';
import {
  ContentGenerationParams,
  ContentGenerationStatus,
  TaskProcessingResult,
  CampaignData,
  ContentTask
} from '../types/content-generation.js';
import {
  createContentGenerationParams,
  formatPersonasForPrompt,
  formatICPsForPrompt,
  ContentGenerationError
} from '../utils/content-generation.js';
import { with<PERSON>ache, CacheKeys, BatchCache } from '../utils/cache.js';

/**
 * Fetches campaign data and validates it exists (with caching)
 */
async function fetchCampaignData(sql: any, campaignId: string): Promise<CampaignData> {
  return withCache(
    CacheKeys.campaign(campaignId),
    async () => {
      const campaignResult = await sql`
        SELECT objective, products, external_research, target_icps, target_personas
        FROM company_campaigns
        WHERE id = ${campaignId}
      `;

      if (campaignResult.length === 0) {
        throw new ContentGenerationError(`Campaign not found: ${campaignId}`);
      }

      return campaignResult[0] as CampaignData;
    },
    10 * 60 * 1000 // Cache for 10 minutes
  );
}

/**
 * Fetches tasks that need content generation
 */
async function fetchTasksNeedingGeneration(
  sql: any,
  campaignId: string,
  companyId: string
): Promise<ContentTask[]> {
  // First, log all tasks for debugging
  const allTasksResult = await sql`
    SELECT id, task_title, task_description, content_type, channel, is_generating, error_generating, content_editor_template
    FROM company_content
    WHERE campaign_id = ${campaignId}
      AND company_id = ${companyId}
  `;

  allTasksResult.forEach((task: any, index: number) => {
    console.log(`  Task ${index + 1}: "${task.task_title}" - Content: ${task.content_editor_template ? 'EXISTS' : 'MISSING'}, Generating: ${task.is_generating}, Error: ${task.error_generating}`);
  });

  // Fetch tasks that need content generation
  const tasksResult = await sql`
    SELECT id, task_title, task_description, content_type, channel, is_generating, error_generating
    FROM company_content
    WHERE campaign_id = ${campaignId}
      AND company_id = ${companyId}
      AND (content_editor_template IS NULL OR content_editor_template = '[]'::jsonb)
      AND is_generating = false
      AND (error_generating IS NULL OR error_generating = false)
  `;

  return tasksResult as ContentTask[];
}

/**
 * Fetches product information for content generation
 */
async function fetchProductInformation(sql: any, campaign: CampaignData, companyId: string): Promise<string> {
  if (!campaign.products || campaign.products.length === 0) {
    return '';
  }

  const productsResult = await sql`
    SELECT name, description, key_features, target_audience FROM products
    WHERE id = ANY(${campaign.products}) AND company_id = ${companyId}
  `;

  return productsResult.map((product: any) => {
    const features = Array.isArray(product.key_features)
      ? product.key_features.map((f: any) => `${f.name}: ${f.value_prop} (${f.differentiation})`).join(', ')
      : '';
    const targetAudience = Array.isArray(product.target_audience)
      ? product.target_audience.join(', ')
      : '';
    return `Product: ${product.name}\nDescription: ${product.description || 'N/A'}\nKey Features: ${features}\nTarget Audience: ${targetAudience}`;
  }).join('\n\n');
}

/**
 * Fetches external research content
 */
async function fetchExternalResearch(sql: any, campaign: CampaignData, companyId: string): Promise<string> {
  if (!campaign.external_research || campaign.external_research.length === 0) {
    return '';
  }

  const researchResult = await sql`
    SELECT description FROM saved_research
    WHERE id = ANY(${campaign.external_research}) AND account_id = ${companyId}
  `;

  return researchResult
    .map((research: any) => JSON.stringify(research.description))
    .join('\n\n');
}

/**
 * Fetches company brand information (with caching)
 */
async function fetchCompanyBrand(sql: any, companyId: string): Promise<object> {
  return withCache(
    CacheKeys.companyBrand(companyId),
    async () => {
      try {
        const brandResult = await sql`
          SELECT brand_profile, messaging_strategy, visual_identity, product_catalog
          FROM company_brand
          WHERE company_id = ${companyId}
        `;
        return brandResult.length > 0 ? brandResult[0] : {};
      } catch (error) {
        console.log('No company brand found, continuing without brand info');
        return {};
      }
    },
    15 * 60 * 1000 // Cache for 15 minutes (brand data changes less frequently)
  );
}

/**
 * Fetches account context data as fallback (with caching)
 */
async function fetchContextData(sql: any, companyId: string): Promise<string> {
  return withCache(
    CacheKeys.accountData(companyId),
    async () => {
      const accountResult = await sql`
        SELECT name, public_data FROM accounts WHERE id = ${companyId}
      `;
      const accountData = accountResult[0];
      return accountData?.public_data?.cleanedText || '';
    },
    15 * 60 * 1000 // Cache for 15 minutes
  );
}

/**
 * Fetches target ICPs
 */
async function fetchTargetICPs(sql: any, campaign: CampaignData): Promise<string> {
  if (!campaign.target_icps || campaign.target_icps.length === 0) {
    return '';
  }

  const icpsResult = await sql`
    SELECT data FROM icps
    WHERE id = ANY(${campaign.target_icps})
  `;

  return formatICPsForPrompt(icpsResult);
}

/**
 * Fetches target personas
 */
async function fetchTargetPersonas(sql: any, campaign: CampaignData): Promise<string> {
  if (!campaign.target_personas || campaign.target_personas.length === 0) {
    return '';
  }

  const personasResult = await sql`
    SELECT data FROM personas
    WHERE id = ANY(${campaign.target_personas})
  `;

  return formatPersonasForPrompt(personasResult);
}

export async function generateTaskContentAsync(sql: any, {
  campaignId,
  companyId,
}: {
  campaignId: string;
  companyId: string;
}) {
  try {
    console.log('🔍 Starting async task content generation for campaign:', campaignId);

    // Fetch campaign and tasks data
    const campaign = await fetchCampaignData(sql, campaignId);
    const tasks = await fetchTasksNeedingGeneration(sql, campaignId, companyId);

    console.log(`🎯 Found ${tasks.length} tasks needing content generation`);

    if (tasks.length === 0) {
      console.log('No tasks found that need content generation');
      return;
    }

    // Fetch all supporting data in parallel for better performance
    const [
      productInformation,
      externalResearchContent,
      companyBrand,
      contextData,
      targetICPs,
      targetPersonas
    ] = await Promise.all([
      fetchProductInformation(sql, campaign, companyId),
      fetchExternalResearch(sql, campaign, companyId),
      fetchCompanyBrand(sql, companyId),
      fetchContextData(sql, companyId),
      fetchTargetICPs(sql, campaign),
      fetchTargetPersonas(sql, campaign)
    ]);

    // Mark all tasks as generating before starting parallel processing
    await Promise.all(
      tasks.map(task =>
        sql`
          UPDATE company_content
          SET is_generating = true, error_generating = false, updated_at = ${Date.now()}
          WHERE id = ${task.id}
        `
      )
    );

    // Generate content for all tasks in parallel
    const contentGenerationPromises = tasks.map(async (task) => {
      try {
        const contentParams = {
          taskTitle: task.task_title,
          taskDescription: task.task_description,
          contentType: task.content_type,
          channel: task.channel,
          campaignGoal: campaign.objective,
          productInformation: productInformation || contextData,
          targetICPs: targetICPs,
          targetPersonas: targetPersonas,
          companyBrand: companyBrand,
          externalResearch: externalResearchContent,
        };

        const generatedContent = await generateTaskContent(contentParams);

        // Update task with generated content
        await sql`
          UPDATE company_content
          SET
            content_editor_template = ${generatedContent.content_editor_template ? sql.json(generatedContent.content_editor_template) : null},
            visual_description = ${generatedContent.visual_description || null},
            seo_keywords = ${generatedContent.seo_keywords ? sql.json(generatedContent.seo_keywords) : null},
            trend_keywords = ${generatedContent.trend_keywords ? sql.json(generatedContent.trend_keywords) : null},
            is_generating = false,
            updated_at = ${Date.now()}
          WHERE id = ${task.id}
        `;

        return { success: true, taskId: task.id, title: task.task_title };
      } catch (error) {
        // Mark task as failed
        console.error(`❌ Content generation failed for task: ${task.id} (${task.task_title})`, error);
        await sql`
          UPDATE company_content
          SET
            is_generating = false,
            error_generating = true,
            updated_at = ${Date.now()}
          WHERE id = ${task.id}
        `;

        return { success: false, taskId: task.id, title: task.task_title, error };
      }
    });

    // Wait for all content generation to complete
    const results = await Promise.all(contentGenerationPromises);

    // Log results summary
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    console.log(`✅ Content generation completed: ${successful} successful, ${failed} failed`);

    if (failed > 0) {
      console.log('Failed tasks:', results.filter(r => !r.success).map(r => r.title));
    }

    console.log('✅ Task content generation completed for campaign:', campaignId);

  } catch (error) {
    console.error('Failed to generate task content:', error);

    // Mark any remaining generating tasks as failed
    try {
      await sql`
        UPDATE company_content
        SET
          is_generating = false,
          error_generating = true,
          updated_at = ${Date.now()}
        WHERE campaign_id = ${campaignId}
          AND company_id = ${companyId}
          AND is_generating = true
      `;
    } catch (updateError) {
      console.error('Failed to update failed tasks:', updateError);
    }

    throw error;
  }
}

// Helper function to get content generation status for a campaign
export async function getContentGenerationStatus(sql: any, campaignId: string): Promise<{
  total: number;
  completed: number;
  generating: number;
  failed: number;
  pending: number;
}> {
  const statusResult = await sql`
    SELECT
      COUNT(*) as total,
      COUNT(CASE WHEN content_editor_template IS NOT NULL AND content_editor_template != '[]'::jsonb THEN 1 END) as completed,
      COUNT(CASE WHEN is_generating = true THEN 1 END) as generating,
      COUNT(CASE WHEN error_generating = true THEN 1 END) as failed,
      COUNT(CASE WHEN (content_editor_template IS NULL OR content_editor_template = '[]'::jsonb) AND is_generating = false AND (error_generating IS NULL OR error_generating = false) THEN 1 END) as pending
    FROM company_content
    WHERE campaign_id = ${campaignId}
  `;

  return statusResult[0] || { total: 0, completed: 0, generating: 0, failed: 0, pending: 0 };
}
