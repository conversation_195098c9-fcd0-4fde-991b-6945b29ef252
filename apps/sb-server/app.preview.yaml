runtime: nodejs20
instance_class: F1
env: standard
service: preview

env_variables:
  SUPABASE_SERVICE_ROLE_KEY: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFiZHRwenNia2Fjb3dsYmRvb2xrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NTQ5NDg2OSwiZXhwIjoyMDcxMDcwODY5fQ.CY7NEBUBk21WWGeZsaQucY_m60DdNkNWc7L5vwRiSjs"
  OPENAI_API_KEY: "********************************************************************************************************************************************************************"
  ZENROWS_API_KEY: "****************************************"
  ZENROWS_API_URL: "https://api.zenrows.com/v1/"
  NEXT_PUBLIC_SUPABASE_URL: "https://qbdtpzsbkacowlbdoolk.supabase.co"
  BASE_URL: "https://preview-dot-psychic-valve-439013-d2.lm.r.appspot.com"
  NEXT_PUBLIC_BASE_URL: "https://api-preview.smartberry.ai"
  PERPLEXITY_API_KEY: "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"
  ZERO_UPSTREAM_DB: "postgres://postgres:<EMAIL>:5432/postgres"
  CLAUDE_API_KEY: "************************************************************************************************************"
  FIRECRAWL_API_KEY: "fc-7580568fc65c44eabcbf9c48e2e0d977"
  LANGFUSE_SECRET_KEY: "******************************************"
  LANGFUSE_PUBLIC_KEY: "pk-lf-5b979bdb-9a18-48a9-8271-a202f85437a4"
  LANGFUSE_BASEURL: "https://cloud.langfuse.com"
  OPENROUTER_API_KEY: "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"
  AYRSHARE_PROFILE_KEY: "***********************************"
  AYRSHARE_API_KEY: "***********************************"
  JIGSAW_API_KEY: "sk_2833c753c68d5a0988885da674331fd011a12f7a0db61e4ab0cdccf1d7115598930a4f008ed889742bb7f02ac58d879fc03fec79fe55d34656ed7f8750130017024AZRPVJ2FWpxVPJ9NQV"
handlers:
  - url: /.*
    script: auto