/**
 * Utilities for generating content prompts in the Studio
 */
import { SelectedDocument } from '~/components/document-selector';
import { Langfuse } from "langfuse";

const langfuse = new Langfuse({
  secretKey: process.env.NEXT_PUBLIC_LANGFUSE_SECRET_KEY,
  publicKey: process.env.NEXT_PUBLIC_LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.NEXT_PUBLIC_LANGFUSE_BASEURL
});
console.log( process.env.LANGFUSE_SECRET_KEY, process.env.LANGFUSE_PUBLIC_KEY, process.env.LANGFUSE_BASE_URL);

export interface PromptGenerationParams {
  taskDescription: string;
  selectedCompanyContent: any;
  selectedPersonas: any[];
  personas: any[];
  selectedIcps: any[];
  icps: any[];
  selectedResearch: any[];
  researchItems: any[];
  selectedDocuments: SelectedDocument[];
  seoKeywords: string[];
  trendKeywords: string[];
  companyBrand: any;
}

/**
 * Generates the content brief section of the prompt
 */
function generateContentBrief(
  taskDescription: string,
  selectedCompanyContent: any,
): string {
  return `
    <CONTENT_BRIEF>
        <Channel>${selectedCompanyContent?.channel || 'Not specified'}</Channel>
        <ContentType>${selectedCompanyContent?.content_type || 'Not specified'}</ContentType>
        <Topic>${taskDescription}</Topic>
    </CONTENT_BRIEF>
  `;
}

/**
 * Generates the audience context section of the prompt
 */
function generateAudienceContext(
  selectedPersonas: any[],
  personas: any[],
  selectedIcps: any[],
  icps: any[],
): string {
  const personasSection =
    selectedPersonas.length > 0
      ? `
    <Personas>
        ${selectedPersonas
          .map((persona) => {
            // Handle both object format (new) and ID format (backward compatibility)
            if (typeof persona === 'string') {
              // Backward compatibility: find persona by ID
              const personaObj = personas.find((p) => p.id === persona);
              if (personaObj) {
                const personaData = personaObj.data && typeof personaObj.data === 'object' ? personaObj.data : {};
                return `<Persona name="${personaObj.name}">\n<Description>${JSON.stringify((personaData as any)?.data) || 'Target audience segment'}</Description>\n</Persona>`;
              }
              return '';
            } else {
              // New format: use the persona object directly
              const personaData = persona.data && typeof persona.data === 'object' ? persona.data : {};
              return `<Persona name="${persona.name}">\n<Description>${JSON.stringify((personaData as any)?.data) || 'Target audience segment'}</Description>\n</Persona>`;
            }
          })
          .filter(Boolean) // Remove empty strings
          .join('\n')}
    </Personas>`
      : '';

  const icpsSection =
    selectedIcps.length > 0
      ? `
    <IdealCustomerProfiles>
        ${selectedIcps
          .map((icp) => {
            // Handle both object format (new) and ID format (backward compatibility)
            if (typeof icp === 'string') {
              // Backward compatibility: find ICP by ID
              const icpObj = icps.find((i) => i.id === icp);
              if (icpObj) {
                const icpData = icpObj.data && typeof icpObj.data === 'object' ? icpObj.data : {};
                return `<ICP name="${icpObj.name}">\n<Description>${JSON.stringify((icpData as any).data) || 'Ideal customer profile'}</Description>\n</ICP>`;
              }
              return '';
            } else {
              // New format: use the ICP object directly
              const icpData = icp.data && typeof icp.data === 'object' ? icp.data : {};
              return `<ICP name="${icp.name}">\n<Description>${JSON.stringify((icpData as any).data) || 'Ideal customer profile'}</Description>\n</ICP>`;
            }
          })
          .filter(Boolean) // Remove empty strings
          .join('\n')}
    </IdealCustomerProfiles>`
      : '';

  return `
    <AUDIENCE_CONTEXT>
        ${personasSection}
        ${icpsSection}
    </AUDIENCE_CONTEXT>
  `;
}

/**
 * Generates the research materials section of the prompt
 */
function generateResearchMaterials(
  selectedResearch: any[],
  researchItems: any[],
): string {
  const researchContent =
    selectedResearch.length > 0
      ? selectedResearch
          .map((research, index) => {
            // Handle both object format (new) and ID format (backward compatibility)
            if (typeof research === 'string') {
              // Backward compatibility: find research by ID
              const researchObj = researchItems.find((r) => r.id === research);
              if (researchObj) {
                const researchData = researchObj.data && typeof researchObj.data === 'object' ? researchObj.data : {};
                return `<research_article_${index + 1}>
          <title>${researchObj.title || researchObj.topic}</title>
          <description>${(researchData as any).description || (researchData as any).summary || 'External research insight'}</description>
          <full_content>${(researchData as any).source_content || ''}</full_content>
          </research_article_${index + 1}>`;
              }
              return '';
            } else {
              // New format: use the research object directly
              const researchData = research.data && typeof research.data === 'object' ? research.data : {};
              return `<research_article_${index + 1}>
          <title>${research.title || research.topic}</title>
          <description>${(researchData as any).description || (researchData as any).summary || 'External research insight'}</description>
          <full_content>${(researchData as any).source_content || ''}</full_content>
          </research_article_${index + 1}>`;
            }
          })
          .filter(Boolean) // Remove empty strings
          .join('\n\n')
      : '<Message>No third-party research was provided.</Message>';

  return `
    <RESEARCH_MATERIALS>
        ${researchContent}
    </RESEARCH_MATERIALS>
  `;
}

/**
 * Generates the company product knowledge base section of the prompt
 */
function generateProductKnowledgeBase(
  selectedDocuments: SelectedDocument[],
): string {
  const documentsContent =
    selectedDocuments.length > 0
      ? selectedDocuments
          .map(
            (doc) =>
              `<Document title="${doc.documentTitle}">\n${doc.content.substring(0, 1000)}${doc.content.length > 1000 ? '...' : ''}\n</Document>`,
          )
          .join('\n\n')
      : '<Message>No company documents were provided.</Message>';

  return `
    <COMPANY_PRODUCT_KNOWLEDGE_BASE>
        ${documentsContent}
    </COMPANY_PRODUCT_KNOWLEDGE_BASE>
  `;
}

/**
 * Generates the keyword strategy section of the prompt
 */
function generateKeywordStrategy(
  seoKeywords: string[],
  trendKeywords: string[],
): string {
  return `
    <KEYWORD_STRATEGY>
        <SEO_Keywords>${seoKeywords.join(', ')}</SEO_Keywords>
        <Trending_Keywords>${trendKeywords.join(', ')}</Trending_Keywords>
    </KEYWORD_STRATEGY>
  `;
}

/**
 * Generates the brand guidelines section of the prompt
 */
function generateBrandGuidelines(companyBrand: any): string {
  return `
    <BRAND_GUIDELINES>
       ${JSON.stringify(companyBrand)}
    </BRAND_GUIDELINES>
  `;
}

/**
 * Generates the complete content generation prompt for Studio using Langfuse
 */
export async function generateStudioContentPromptWithLangfuse(
  params: PromptGenerationParams,
  promptName: string = "generate_content_body_v2"
): Promise<string> {
  const {
    taskDescription,
    selectedCompanyContent,
    selectedPersonas,
    personas,
    selectedIcps,
    icps,
    selectedResearch,
    researchItems,
    selectedDocuments,
    seoKeywords,
    trendKeywords,
    companyBrand,
  } = params;

  try {
    console.log('🔍 Fetching prompt from Langfuse:', promptName);

    // Get the production prompt from Langfuse
    const prompt: any = await langfuse.getPrompt(promptName, undefined, { label: "production" });

    if (!prompt) {
      throw new Error(`Prompt "${promptName}" not found in Langfuse`);
    }

    console.log('✅ Prompt fetched successfully from Langfuse');
    // Generate the context blocks for compilation
    const personasBlock = generateAudienceContext(selectedPersonas, personas, [], []);
    const icpsBlock = generateAudienceContext([], [], selectedIcps, icps);
    const researchBlock = generateResearchMaterials(selectedResearch, researchItems);
    const documentsBlock = generateProductKnowledgeBase(selectedDocuments);

    // Compile the prompt with the variables
    const compiledPrompt = await prompt.compile({
      channel: selectedCompanyContent?.channel || 'Not specified',
      content_type: selectedCompanyContent?.content_type || 'Not specified',
      task_description: taskDescription,
      personas_block: personasBlock,
      icps_block: icpsBlock,
      research_block: researchBlock,
      documents_block: documentsBlock,
      seo_keywords: seoKeywords.join(', '),
      trend_keywords: trendKeywords.join(', '),
      brand_guidelines: JSON.stringify(companyBrand)
    });

    console.log('📝 Compiled prompt type:', typeof compiledPrompt);
    console.log('📝 Compiled prompt:', compiledPrompt);

    // Ensure we return a string
    const promptString = typeof compiledPrompt === 'string' ? compiledPrompt : String(compiledPrompt);
    return promptString;
  } catch (error) {
    console.error('❌ Error fetching Langfuse prompt, falling back to hardcoded prompt:', error);
    console.log('🔄 Using fallback prompt generation...');

    // Fallback to the existing hardcoded prompt if Langfuse fails
    const fallbackPrompt = generateStudioContentPrompt(params);
    console.log('✅ Fallback prompt generated successfully');

    return fallbackPrompt;
  }
}

/**
 * Generates the complete content generation prompt for Studio (legacy hardcoded version)
 */
export function generateStudioContentPrompt(
  params: PromptGenerationParams,
): string {
  const {
    taskDescription,
    selectedCompanyContent,
    selectedPersonas,
    personas,
    selectedIcps,
    icps,
    selectedResearch,
    researchItems,
    selectedDocuments,
    seoKeywords,
    trendKeywords,
    companyBrand,
  } = params;

  const contextBlock = `
    <CONTEXT_BLOCK>
      ${generateContentBrief(taskDescription, selectedCompanyContent)}
      ${generateAudienceContext(selectedPersonas, personas, selectedIcps, icps)}
      ${generateResearchMaterials(selectedResearch, researchItems)}
      ${generateProductKnowledgeBase(selectedDocuments)}
      ${generateKeywordStrategy(seoKeywords, trendKeywords)}
      ${generateBrandGuidelines(companyBrand)}
    </CONTEXT_BLOCK>
  `;

  const instructions = `
    You are good at writing content. Ignore the previous instruction "Because the document is empty, first update the empty block before adding new blocks."
    Do not stop until you have completed generating the content you have been asked to generate. Do not do partial creations, only full generations. For example, if you are asked to generate a blog post, create the full blog post. 
    This is the most critical instruction. Do not stop until you have completed generating the content you have been asked to generate. Do not do partial creations, only full generations. For example, if you are asked to generate a blog post, create the full blog post. 
    Do not try to insert images or videos into the content. For Threads, carousels, or similar content, simply insert everything delineated by some character, then write out everything as if its one page. The user will handle creating the thread or similar on the appropriate platform.
  `;

  const taskInstructions = `
    You are "Cognitive Creator," an expert AI copywriter and content strategist. Your core function is to synthesize brand information, audience data, and research into high-performing content tailored for specific marketing channels. You follow all instructions with precision.

    <TASK>
    Synthesize all information within the <CONTEXT_BLOCK> to create engaging content.

    **PRIMARY DIRECTIVES:**
    1.  **Adhere to Brand:** The <BRAND_GUIDELINES> are the highest priority. The specified <Voice> and <Personality> must be perfectly reflected in the output. This is non-negotiable.
    2.  **Target the Audience:** Tailor the language, examples, and tone specifically to the <AUDIENCE_CONTEXT>. Address their needs and pain points directly.
    3.  **Position as the Solution:** Use the <RESEARCH_MATERIALS> for context, stats, and credibility. ALWAYS position the company/product from the <COMPANY_PRODUCT_KNOWLEDGE_BASE> as the primary solution to problems identified in the research. Never promote third parties.
    4.  **Incorporate Keywords:** Naturally weave terms from the <KEYWORD_STRATEGY> into the content.
    5.  **Be Factual:** Ensure any product or company claims are supported by the <COMPANY_PRODUCT_KNOWLEDGE_BASE>. Do not invent features or facts.
    6.  **Do Not Invent Social Proof:** Do not create fake customer names or quotes. You can suggest a placeholder like "[Insert customer testimonial here]" if appropriate for the content type.

    **CHANNEL-SPECIFIC RULES:**
    Based on the <Channel> specified in the <CONTENT_BRIEF>, you must follow these structural rules:

    *   **If Channel is "LinkedIn Post":**
        *   **Structure:** Start with a strong hook. Use short paragraphs (1-2 sentences). Use bullet points or numbered lists for readability. End with a question to drive engagement.
        *   **Length:** 150-250 words.
        *   **Tone:** Professional, insightful, and value-driven.
        *   **Hashtags:** Provide 3-5 relevant, professional hashtags.

    *   **If Channel is "Tweet" or "X Post":**
        *   **Structure:** A short, punchy, and engaging message.
        *   **Length:** Strictly under 280 characters.
        *   **Tone:** Conversational and concise. Emojis are acceptable if they match the brand personality.
        *   **Hashtags:** Provide 1-3 highly relevant hashtags.

    *   **If Channel is "Blog Post" or "Article":**
        *   **Structure:** Create a compelling H1 title. Write a short introduction. Structure the main content with 3-4 H2 subheadings. Conclude with a summary and a strong call_to_action.
        *   **Length:** 600-1000 words.
        *   **Tone:** Informative, in-depth, and authoritative, aligned with the brand voice.
        *   **SEO:** Suggest 5-7 relevant meta tags in the output.

    *   **If Channel is "Facebook Ad":**
        *   **Structure:** Provide three distinct components: a short, attention-grabbing headline, persuasive primary_text focusing on benefits, and a direct call_to_action_text.
        *   **Tone:** Persuasive, clear, and benefit-driven.

    *   **If Channel is not specified or doesn't match above:**
        *   **Structure:** Create a general-purpose piece of content with a clear beginning, middle, and end.
        *   **Tone:** Follow the brand voice.
        *   **Action:** End with a clear call-to-action.

    </TASK>
  `;

  return `${instructions}\n${contextBlock}\n${taskInstructions}`;
}
