'use client'

import { useMemo, useState, useCallback } from 'react';
import { getCoreRowModel, useReactTable } from "@tanstack/react-table"
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';

import { useZero } from '~/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { CompanyContent } from '~/types/company-content';

import { createRecentActivityColumns } from './columns';
import { TaskEditDialog } from '../../../../tasks/components/task-edit-dialog';
import { getRecentTasks } from './utils';
import {
  RecentActivitySkeleton,
  RecentActivityEmpty,
  RecentActivityHeader,
  RecentActivityTable,
} from './components';

/**
 * Modal component for task editing
 * Kept inline as it's tightly coupled to the main component's state
 */
const RecentActivityModal = ({
  selectedTask,
  isDialogOpen,
  onOpenChange
}: {
  selectedTask: CompanyContent | null;
  isDialogOpen: boolean;
  onOpenChange: (open: boolean) => void;
}) => (
  selectedTask ? (
    <TaskEditDialog
      task={selectedTask}
      open={isDialogOpen}
      onOpenChange={onOpenChange}
    />
  ) : null
);

/**
 * Main Recent Activity component
 * Displays the 5 most recently updated tasks in a dashboard format
 */
export function RecentActivity() {
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();

  // Modal state for task editing
  const [selectedTask, setSelectedTask] = useState<CompanyContent | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Memoized function to handle task click
  const handleTaskClick = useCallback((task: CompanyContent) => {
    setSelectedTask(task);
    setIsDialogOpen(true);
  }, []);

  // Fetch recent tasks using Zero Sync Engine
  // Remove TTL to ensure real-time updates for dashboard data
  const [company_content] = useZeroQuery(
    zero.query.company_content
      .where('company_id', '=', workspace.account.id)
  );

  // Process and sort the data to get the 5 most recently updated tasks
  const recentTasks = useMemo(() => {
    try {
      const tasks = getRecentTasks(company_content);
      // Debug: Log the processed tasks to help with troubleshooting
      console.log('Recent tasks processed:', tasks.map(t => ({
        id: t.id,
        title: t.task_title,
        status: t.status,
        assigned_to: t.assigned_to,
        updated_at: t.updated_at
      })));
      return tasks;
    } catch (err) {
      console.error('Error processing recent tasks:', err);
      return [];
    }
  }, [company_content]);

  // Memoized columns to prevent unnecessary re-renders
  const columns = useMemo(
    () => createRecentActivityColumns({ onTaskClick: handleTaskClick }),
    [handleTaskClick]
  );

  const table = useReactTable({
    data: recentTasks,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  // Loading state
  if (!company_content) {
    return <RecentActivitySkeleton />;
  }

  // Empty state
  if (recentTasks.length === 0) {
    return <RecentActivityEmpty />;
  }

  return (
    <div className="space-y-4">
      <RecentActivityHeader />
      <RecentActivityTable table={table} columns={columns} />

      {/* Task Edit Modal */}
      <RecentActivityModal
        selectedTask={selectedTask}
        isDialogOpen={isDialogOpen}
        onOpenChange={setIsDialogOpen}
      />
    </div>
  );
}
