'use client';

import { useMemo } from 'react';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useQuery } from '@tanstack/react-query';
import { useZero } from './use-zero';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

export interface TrialStatus {
  hasActiveSubscription: boolean;
  isTrialing: boolean;
  trialExpired: boolean;
  trialExpiringSoon: boolean;
  canAccessApp: boolean;
  daysUntilExpiration: number | null;
  isLoading: boolean;
}

export interface TrialBadgeStatus {
  show: boolean;
  variant: 'success' | 'warning' | 'destructive';
  message: string;
  isLoading: boolean;
}

export function useTrialStatus(): TrialStatus {
  // SUPABASE VERSION - Simplified for debugging
  const workspace = useTeamAccountWorkspace();
  const supabase = useSupabase();

  const { data: subscriptions, isLoading } = useQuery({
    queryKey: ['subscriptions', workspace.account.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('account_id', workspace.account.id);

      if (error) {
        console.error(`Failed to fetch subscriptions: ${error.message}`);
        throw new Error(`Failed to fetch subscriptions: ${error.message}`);
      }

      // Log subscription data for debugging
      console.log('Fetched subscriptions:', data);
      
      return data;
    },
    enabled: !!workspace.account.id,
  });

  return useMemo(() => {
    if (isLoading) {
      return {
        hasActiveSubscription: false,
        isTrialing: false,
        trialExpired: false,
        trialExpiringSoon: false,
        canAccessApp: false, // Block access during loading to prevent content flash
        daysUntilExpiration: null,
        isLoading: true,
      };
    }

    const subscription = subscriptions?.find((sub) => sub.active) || subscriptions?.[0];
    
    if (!subscription) {
      console.log('No subscription found for account:', workspace.account.id);
      return {
        hasActiveSubscription: false,
        isTrialing: false,
        trialExpired: false,
        trialExpiringSoon: false,
        canAccessApp: false,
        daysUntilExpiration: null,
        isLoading: false,
      };
    }

    const isTrialing = subscription.status === 'trialing';
    const hasActiveSubscription = subscription.status === 'active' || isTrialing;
    
    // Log subscription status for debugging
    console.log('Subscription status:', {
      id: subscription.id,
      status: subscription.status,
      isTrialing,
      hasActiveSubscription,
    });
    
    let trialExpired = false;
    let trialExpiringSoon = false;
    let daysUntilExpiration: number | null = null;

    if (subscription.trial_ends_at) {
      // Zero-sync stores timestamps as numbers (milliseconds since epoch)
      const trialEndsAt = typeof subscription.trial_ends_at === 'number'
        ? subscription.trial_ends_at
        : new Date(subscription.trial_ends_at).getTime();
      const now = Date.now();
      const timeDiff = trialEndsAt - now;
      daysUntilExpiration = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

      trialExpired = timeDiff <= 0;
      trialExpiringSoon = timeDiff > 0 && daysUntilExpiration <= 3;
      
      // Log trial expiration details for debugging
      console.log('Trial expiration details:', {
        trialEndsAt: new Date(trialEndsAt).toISOString(),
        now: new Date(now).toISOString(),
        daysUntilExpiration,
        trialExpired,
        trialExpiringSoon,
      });
    }

    return {
      hasActiveSubscription,
      isTrialing,
      trialExpired,
      trialExpiringSoon,
      canAccessApp: hasActiveSubscription && (!isTrialing || !trialExpired),
      daysUntilExpiration,
      isLoading: false,
    };
  }, [subscriptions, isLoading, workspace.account.id]);
}

export function useTrialBadge(): TrialBadgeStatus {
  // Use the existing useTrialStatus hook but handle loading differently for the badge
  const { isTrialing, trialExpired, trialExpiringSoon, daysUntilExpiration, hasActiveSubscription, isLoading } = useTrialStatus();

  // Get the raw subscription to check for cancellation status
  const workspace = useTeamAccountWorkspace();
  const supabase = useSupabase();
  const { data: subscriptions } = useQuery({
    queryKey: ['subscriptions', workspace.account.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('account_id', workspace.account.id);

      if (error) {
        throw new Error(`Failed to fetch subscriptions: ${error.message}`);
      }
      
      return data;
    },
    enabled: !!workspace.account.id,
  });

  // Check if subscription is canceled but still active during trial
  const subscription = subscriptions?.find((sub) => sub.active) || subscriptions?.[0];
  const isCanceledDuringTrial = subscription?.cancel_at_period_end && isTrialing;

  return useMemo(() => {
    // For the trial badge, we can show it even during loading if we have cached data
    // Only hide during loading if we have no data at all
    if (isLoading && !isTrialing && !trialExpired && !hasActiveSubscription) {
      return { show: false, variant: 'success', message: '', isLoading: true };
    }

    // If no subscription exists at all (new user), don't show any badge
    // This prevents showing "Trial expired" for users who just signed up
    if (!hasActiveSubscription && !isTrialing) {
      return { show: false, variant: 'success', message: '', isLoading: false };
    }

    // IMPORTANT: Check for active paid subscription FIRST
    // This ensures that if a user has a paid subscription, we don't show any trial badge
    // regardless of whether their trial expired before subscribing
    if (hasActiveSubscription && !isTrialing) {
      console.log('Hiding trial badge: User has active paid subscription');
      return { show: false, variant: 'success', message: '', isLoading: false };
    }

    // Handle the case where subscription is canceled during trial
    // Show a special badge indicating the trial will not auto-renew
    if (isCanceledDuringTrial) {
      console.log('Showing canceled trial badge: Subscription canceled during trial');
      
      // If trial is expiring soon, show warning badge with cancellation message
      if (trialExpiringSoon && daysUntilExpiration !== null) {
        const days = daysUntilExpiration === 1 ? 'day' : 'days';
        return {
          show: true,
          variant: 'warning',
          message: `Trial expires in ${daysUntilExpiration} ${days} (canceled)`,
          isLoading: false,
        };
      }
      
      // If trial is not expiring soon, show regular badge with cancellation indicator
      if (daysUntilExpiration !== null && daysUntilExpiration > 3) {
        const days = daysUntilExpiration === 1 ? 'day' : 'days';
        return {
          show: true,
          variant: 'success',
          message: `Trial expires in ${daysUntilExpiration} ${days} (canceled)`,
          isLoading: false,
        };
      }
    }

    // If trial has expired (and user doesn't have an active paid subscription), show expired badge
    if (trialExpired) {
      return { show: true, variant: 'destructive', message: 'Trial expired', isLoading: false };
    }

    // If trialing and expiring soon, show warning badge
    if (isTrialing && trialExpiringSoon && daysUntilExpiration !== null) {
      const days = daysUntilExpiration === 1 ? 'day' : 'days';
      return {
        show: true,
        variant: 'warning',
        message: `Trial expires in ${daysUntilExpiration} ${days}`,
        isLoading: false,
      };
    }

    // If trialing with more than 3 days left, show success badge
    if (isTrialing && daysUntilExpiration !== null && daysUntilExpiration > 3) {
      const days = daysUntilExpiration === 1 ? 'day' : 'days';
      return {
        show: true,
        variant: 'success',
        message: `Trial expires in ${daysUntilExpiration} ${days}`,
        isLoading: false,
      };
    }

    return { show: false, variant: 'success', message: '', isLoading: false };
  }, [isTrialing, trialExpired, trialExpiringSoon, daysUntilExpiration, hasActiveSubscription, isLoading, isCanceledDuringTrial]);
}
